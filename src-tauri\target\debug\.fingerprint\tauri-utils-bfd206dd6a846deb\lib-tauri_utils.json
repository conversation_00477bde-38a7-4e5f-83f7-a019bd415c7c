{"rustc": 3062648155896360161, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 12518800248535767337, "deps": [[561782849581144631, "html5ever", false, 17010769824072371667], [1200537532907108615, "url<PERSON><PERSON>n", false, 15457912715210496490], [3060637413840920116, "proc_macro2", false, 16573490135189604103], [3129130049864710036, "memchr", false, 15293772899464542499], [3150220818285335163, "url", false, 6593312651437174297], [3191507132440681679, "serde_untagged", false, 10295787782563159232], [4899080583175475170, "semver", false, 12985120894860853875], [5986029879202738730, "log", false, 15023198677380483266], [6213549728662707793, "serde_with", false, 18246346174614082811], [6262254372177975231, "kuchiki", false, 11859737323605946472], [6606131838865521726, "ctor", false, 3832924398591137448], [6913375703034175521, "schemars", false, 16732340611100675589], [7170110829644101142, "json_patch", false, 4739291377758425400], [8319709847752024821, "uuid", false, 9434787631382329335], [8786711029710048183, "toml", false, 338529948346107867], [9010263965687315507, "http", false, 11039511902789686861], [9451456094439810778, "regex", false, 1065499532444342601], [9689903380558560274, "serde", false, 9383613698865521081], [10806645703491011684, "thiserror", false, 6668967661066548470], [11655476559277113544, "cargo_metadata", false, 16251179424105777485], [11989259058781683633, "dunce", false, 10333777902361752181], [13625485746686963219, "anyhow", false, 17939884661928602801], [14132538657330703225, "brotli", false, 8214844146941754534], [15367738274754116744, "serde_json", false, 548601842232322339], [15622660310229662834, "walkdir", false, 226726883387927066], [17146114186171651583, "infer", false, 8003028046184081634], [17155886227862585100, "glob", false, 4601722822262977862], [17186037756130803222, "phf", false, 9094318008645669462], [17990358020177143287, "quote", false, 10461966970759974999]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-bfd206dd6a846deb\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}