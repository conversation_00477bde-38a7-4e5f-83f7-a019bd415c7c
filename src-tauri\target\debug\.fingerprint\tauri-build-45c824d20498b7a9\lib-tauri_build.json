{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12844171384082609526, "deps": [[4899080583175475170, "semver", false, 12985120894860853875], [6913375703034175521, "schemars", false, 16732340611100675589], [7170110829644101142, "json_patch", false, 4739291377758425400], [8786711029710048183, "toml", false, 338529948346107867], [9689903380558560274, "serde", false, 9383613698865521081], [11050281405049894993, "tauri_utils", false, 2396713609722994007], [12714016054753183456, "tauri_winres", false, 2083576003474789555], [13077543566650298139, "heck", false, 11354297438612443554], [13475171727366188400, "cargo_toml", false, 9532857226721031482], [13625485746686963219, "anyhow", false, 17939884661928602801], [15367738274754116744, "serde_json", false, 548601842232322339], [15622660310229662834, "walkdir", false, 226726883387927066], [16928111194414003569, "dirs", false, 12748626123542373273], [17155886227862585100, "glob", false, 4601722822262977862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-45c824d20498b7a9\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}