{"rustc": 3062648155896360161, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 3303257178207977090, "deps": [[1615478164327904835, "pin_utils", false, 8395521421942436255], [1906322745568073236, "pin_project_lite", false, 17891959561919357710], [6955678925937229351, "slab", false, 14787973034760382087], [7620660491849607393, "futures_core", false, 4941160812658996078], [10565019901765856648, "futures_macro", false, 1926846725317101637], [16240732885093539806, "futures_task", false, 14943222381606933846]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-208e815303b8d6d7\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}