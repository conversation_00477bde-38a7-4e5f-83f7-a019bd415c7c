# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-from-bytes"
description = "Enables the from_bytes command without any pre-configured scope."
commands.allow = ["from_bytes"]

[[permission]]
identifier = "deny-from-bytes"
description = "Denies the from_bytes command without any pre-configured scope."
commands.deny = ["from_bytes"]
