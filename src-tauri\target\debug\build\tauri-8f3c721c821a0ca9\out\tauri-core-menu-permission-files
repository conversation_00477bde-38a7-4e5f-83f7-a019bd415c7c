["\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\menu\\autogenerated\\default.toml"]