{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8985965904563823836, "deps": [[3060637413840920116, "proc_macro2", false, 16573490135189604103], [3150220818285335163, "url", false, 6593312651437174297], [4899080583175475170, "semver", false, 12985120894860853875], [7170110829644101142, "json_patch", false, 4739291377758425400], [7392050791754369441, "ico", false, 7475844171303784353], [8319709847752024821, "uuid", false, 9434787631382329335], [9689903380558560274, "serde", false, 9383613698865521081], [9857275760291862238, "sha2", false, 2966569939969370980], [10806645703491011684, "thiserror", false, 6668967661066548470], [11050281405049894993, "tauri_utils", false, 2396713609722994007], [12687914511023397207, "png", false, 18093638915678647426], [13077212702700853852, "base64", false, 3704075103290783005], [14132538657330703225, "brotli", false, 8214844146941754534], [15367738274754116744, "serde_json", false, 548601842232322339], [15622660310229662834, "walkdir", false, 226726883387927066], [17990358020177143287, "quote", false, 10461966970759974999], [18149961000318489080, "syn", false, 5385601576493088642]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-b9c3bfdde93795d8\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}