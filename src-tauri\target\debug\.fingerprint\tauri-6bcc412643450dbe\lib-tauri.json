{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 15842239878754744876], [442785307232013896, "tauri_runtime", false, 7041282790766300674], [1200537532907108615, "url<PERSON><PERSON>n", false, 11963149627374069348], [3150220818285335163, "url", false, 3104463379441451225], [4143744114649553716, "raw_window_handle", false, 2523135429908484991], [4341921533227644514, "muda", false, 4416840981006554492], [4919829919303820331, "serialize_to_javascript", false, 13906648703476088155], [5986029879202738730, "log", false, 15023198677380483266], [7752760652095876438, "tauri_runtime_wry", false, 13598170239331910416], [8539587424388551196, "webview2_com", false, 3457275967422414224], [9010263965687315507, "http", false, 11039511902789686861], [9228235415475680086, "tauri_macros", false, 7151164055904413563], [9538054652646069845, "tokio", false, 8713892192606511934], [9689903380558560274, "serde", false, 8907299124283732244], [9920160576179037441, "getrandom", false, 310150501046614117], [10229185211513642314, "mime", false, 16472311026408746956], [10629569228670356391, "futures_util", false, 11820571555869320671], [10755362358622467486, "build_script_build", false, 13692521871215333223], [10806645703491011684, "thiserror", false, 6668967661066548470], [11050281405049894993, "tauri_utils", false, 5989626050602261291], [11989259058781683633, "dunce", false, 10333777902361752181], [12565293087094287914, "window_vibrancy", false, 3870095856308709434], [12986574360607194341, "serde_repr", false, 15975428991677852167], [13077543566650298139, "heck", false, 11354297438612443554], [13116089016666501665, "windows", false, 5027385780331850070], [13625485746686963219, "anyhow", false, 17939884661928602801], [15367738274754116744, "serde_json", false, 3727635998279811524], [16928111194414003569, "dirs", false, 15665358830444531615], [17155886227862585100, "glob", false, 4601722822262977862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-6bcc412643450dbe\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}