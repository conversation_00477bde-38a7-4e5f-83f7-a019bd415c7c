{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 11699925393555138086], [442785307232013896, "tauri_runtime", false, 7041282790766300674], [3150220818285335163, "url", false, 3104463379441451225], [3722963349756955755, "once_cell", false, 3531247951013610750], [4143744114649553716, "raw_window_handle", false, 2523135429908484991], [5986029879202738730, "log", false, 15023198677380483266], [7752760652095876438, "build_script_build", false, 6685676406408053928], [8539587424388551196, "webview2_com", false, 3457275967422414224], [9010263965687315507, "http", false, 11039511902789686861], [11050281405049894993, "tauri_utils", false, 5989626050602261291], [13116089016666501665, "windows", false, 5027385780331850070], [13223659721939363523, "tao", false, 12816557003321192019], [14794439852947137341, "wry", false, 3048648752661359955]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-4f162457830a10a1\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}