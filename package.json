{"name": "tauri-vue-starter", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "vue-tsc --noEmit && vite build", "dev": "vite", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-opener": "^2.4.0", "@vueuse/core": "^13.5.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/carbon": "^1.2.10", "@tauri-apps/cli": "^2.6.2", "@types/node": "^22.16.4", "@unocss/preset-icons": "^66.4.2", "@unocss/reset": "^66.4.2", "@vitejs/plugin-vue": "^6.0.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "unocss": "^66.4.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-router": "^0.12.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.7", "vite-plugin-vue-layouts": "^0.11.0", "vue-tsc": "^3.0.1"}}