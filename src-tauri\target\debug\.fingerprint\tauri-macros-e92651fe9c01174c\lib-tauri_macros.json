{"rustc": 3062648155896360161, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 8778928345189965635, "deps": [[3060637413840920116, "proc_macro2", false, 16573490135189604103], [7341521034400937459, "tauri_codegen", false, 9065434891351627671], [11050281405049894993, "tauri_utils", false, 2396713609722994007], [13077543566650298139, "heck", false, 11354297438612443554], [17990358020177143287, "quote", false, 10461966970759974999], [18149961000318489080, "syn", false, 5385601576493088642]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-e92651fe9c01174c\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}