["\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\path\\autogenerated\\default.toml"]