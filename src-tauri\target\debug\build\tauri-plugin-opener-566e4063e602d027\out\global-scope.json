{"$schema": "http://json-schema.org/draft-07/schema#", "title": "OpenerScopeEntry", "description": "Opener scope entry.", "anyOf": [{"type": "object", "required": ["url"], "properties": {"url": {"description": "A URL that can be opened by the webview when using the Opener APIs.\n\nWildcards can be used following the UNIX glob pattern.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}, "app": {"description": "An application to open this url with, for example: firefox.", "allOf": [{"$ref": "#/definitions/Application"}]}}}, {"type": "object", "required": ["path"], "properties": {"path": {"description": "A path that can be opened by the webview when using the Opener APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, "app": {"description": "An application to open this path with, for example: xdg-open.", "allOf": [{"$ref": "#/definitions/Application"}]}}}], "definitions": {"Application": {"description": "Opener scope application.", "anyOf": [{"description": "Open in default application.", "type": "null"}, {"description": "If true, allow open with any application.", "type": "boolean"}, {"description": "Allow specific application to open with.", "type": "string"}]}}}