{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 12716134240572795321], [3150220818285335163, "url", false, 3104463379441451225], [4143744114649553716, "raw_window_handle", false, 2523135429908484991], [7606335748176206944, "dpi", false, 10711395204648486590], [9010263965687315507, "http", false, 11039511902789686861], [9689903380558560274, "serde", false, 8907299124283732244], [10806645703491011684, "thiserror", false, 6668967661066548470], [11050281405049894993, "tauri_utils", false, 5989626050602261291], [13116089016666501665, "windows", false, 5027385780331850070], [15367738274754116744, "serde_json", false, 3727635998279811524], [16727543399706004146, "cookie", false, 16692351731011611202]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-2da5cb0141d14874\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}