import { defineConfig, presetWind3, presetIcons, presetTypography, presetAttributify, transformerDirectives } from 'unocss'

export default defineConfig({
  presets: [
    presetWind3(), // 包含了类似Tailwind CSS的实用工具类
    presetTypography(), // 使用prose类名激活排版样式
    presetAttributify, // 支持在HTML标签上直接使用类名
    presetIcons({
      collections: {
        carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
      },
      scale: 1.2,
      warn: true,
    }),
  ],
  transformers: [
    transformerDirectives(),
  ],
  shortcuts: [
    // 保持原有的组件样式
    {
      'btn': 'inline-block cursor-pointer rounded bg-[#3498db] px-4 py-1 text-white outline-none hover:bg-[#2980b9] disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
      'icon-btn': 'inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:text-[#3498db] hover:opacity-100',
    },
  ],
  theme: {
    colors: {
      // 可以在这里定义自定义颜色
    },
  },
  // variants: [
  //   // 支持dark模式
  //   (matcher) => {
  //     if (!matcher.startsWith('dark:')) return matcher
  //     return {
  //       matcher: matcher.slice(5),
  //       selector: s => `.dark ${s}`,
  //     }
  //   },
  // ],
})
