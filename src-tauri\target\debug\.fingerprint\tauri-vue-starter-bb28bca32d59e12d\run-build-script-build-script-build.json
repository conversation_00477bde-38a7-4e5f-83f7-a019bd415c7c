{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 13692521871215333223], [3935545708480822364, "build_script_build", false, 13858124379781061379], [9968676344646433769, "build_script_build", false, 14918844028505602689]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-vue-starter-bb28bca32d59e12d\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}