{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 10075617358883313196, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[3935545708480822364, "tauri_plugin_opener", false, 7934716387314606799], [9689903380558560274, "serde", false, 8907299124283732244], [9968676344646433769, "build_script_build", false, 952822618571874385], [10755362358622467486, "tauri", false, 18318417798944901732], [15367738274754116744, "serde_json", false, 3727635998279811524]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-vue-starter-1b22a1626830fa4b\\dep-lib-tauri_starter_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}