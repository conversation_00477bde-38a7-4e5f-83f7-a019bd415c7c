["\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\debug\\build\\tauri-8f3c721c821a0ca9\\out\\permissions\\app\\autogenerated\\default.toml"]